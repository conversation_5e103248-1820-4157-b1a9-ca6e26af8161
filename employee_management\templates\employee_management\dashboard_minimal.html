<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-users me-3"></i>
                    لوحة تحكم إدارة الموظفين
                </h1>
                
                <div class="alert alert-success" role="alert">
                    <h4 class="alert-heading">مرحباً بك!</h4>
                    <p>تم تحميل لوحة تحكم إدارة الموظفين بنجاح.</p>
                    <hr>
                    <p class="mb-0">هذه صفحة اختبار للتأكد من أن النظام يعمل بشكل صحيح.</p>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mt-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h5>إجمالي الموظفين</h5>
                                <h2>{{ total_employees|default:0 }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-3x mb-3"></i>
                                <h5>الأقسام</h5>
                                <h2>{{ total_departments|default:0 }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-briefcase fa-3x mb-3"></i>
                                <h5>المسميات الوظيفية</h5>
                                <h2>{{ total_job_titles|default:0 }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus fa-3x mb-3"></i>
                                <h5>موظفين جدد هذا الشهر</h5>
                                <h2>{{ new_employees_this_month|default:0 }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h3>إجراءات سريعة</h3>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                                <h5>إضافة موظف جديد</h5>
                                <button class="btn btn-primary">إضافة</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-3x text-success mb-3"></i>
                                <h5>إضافة قسم جديد</h5>
                                <button class="btn btn-success">إضافة</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-briefcase fa-3x text-info mb-3"></i>
                                <h5>إضافة وظيفة جديدة</h5>
                                <button class="btn btn-info">إضافة</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-3x text-warning mb-3"></i>
                                <h5>البحث في الموظفين</h5>
                                <button class="btn btn-warning">بحث</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activities -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>النشاطات الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                {% if recent_activities %}
                                    {% for activity in recent_activities %}
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-{{ activity.icon }} text-primary me-3"></i>
                                        <div>
                                            <div class="fw-bold">{{ activity.title }}</div>
                                            <small class="text-muted">{{ activity.description }}</small>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted">لا توجد نشاطات حديثة</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Departments Overview -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>نظرة عامة على الأقسام</h5>
                            </div>
                            <div class="card-body">
                                {% if departments %}
                                    {% for dept in departments %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <div class="fw-bold">{{ dept.dept_name }}</div>
                                            <small class="text-muted">{{ dept.employee_count }} موظف</small>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary">عرض</button>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted">لا توجد أقسام مسجلة</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation to Other HR Modules -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h3>وحدات الموارد البشرية الأخرى</h3>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                <h5>إدارة الإجازات</h5>
                                <p>إدارة طلبات الإجازات وأرصدة الموظفين</p>
                                <button class="btn btn-light">الانتقال</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x mb-3"></i>
                                <h5>نظام الحضور والانصراف</h5>
                                <p>تتبع حضور الموظفين وساعات العمل</p>
                                <button class="btn btn-light">الانتقال</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                                <h5>إدارة الرواتب</h5>
                                <p>حساب ومعالجة رواتب الموظفين</p>
                                <button class="btn btn-light">الانتقال</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-5">
                    <p class="text-muted">© 2025 نظام الدولية انترناشونال - جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
