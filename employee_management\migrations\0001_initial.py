# Generated by Django 5.0.14 on 2025-06-22 14:15

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('dept_code', models.AutoField(primary_key=True, serialize=False, verbose_name='رمز القسم')),
                ('dept_name', models.CharField(max_length=100, unique=True, verbose_name='اسم القسم')),
                ('dept_name_en', models.CharField(blank=True, max_length=100, verbose_name='اسم القسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('parent_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sub_departments', to='employee_management.department', verbose_name='القسم الرئيسي')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'db_table': 'employee_management_department',
                'ordering': ['dept_name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('emp_code', models.AutoField(primary_key=True, serialize=False, verbose_name='رمز الموظف')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='الرقم الوظيفي')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('middle_name', models.CharField(blank=True, max_length=50, verbose_name='الاسم الأوسط')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('first_name_en', models.CharField(blank=True, max_length=50, verbose_name='الاسم الأول بالإنجليزية')),
                ('middle_name_en', models.CharField(blank=True, max_length=50, verbose_name='الاسم الأوسط بالإنجليزية')),
                ('last_name_en', models.CharField(blank=True, max_length=50, verbose_name='اسم العائلة بالإنجليزية')),
                ('national_id', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^\\d{10,20}$', 'رقم الهوية يجب أن يكون من 10-20 رقم')], verbose_name='رقم الهوية الوطنية')),
                ('passport_number', models.CharField(blank=True, max_length=20, verbose_name='رقم جواز السفر')),
                ('email', models.EmailField(max_length=254, unique=True, validators=[django.core.validators.EmailValidator()], verbose_name='البريد الإلكتروني')),
                ('phone_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='رقم الهاتف')),
                ('mobile_number', models.CharField(blank=True, max_length=15, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الجوال غير صحيح')], verbose_name='رقم الجوال')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('state', models.CharField(max_length=50, verbose_name='المنطقة/الولاية')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='Saudi Arabia', max_length=50, verbose_name='البلد')),
                ('date_of_birth', models.DateField(verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('marital_status', models.CharField(choices=[('single', 'أعزب'), ('married', 'متزوج'), ('divorced', 'مطلق'), ('widowed', 'أرمل')], max_length=10, verbose_name='الحالة الاجتماعية')),
                ('nationality', models.CharField(default='Saudi', max_length=50, verbose_name='الجنسية')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('probation_end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء فترة التجربة')),
                ('termination_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الخدمة')),
                ('employment_status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('terminated', 'منتهي الخدمة'), ('suspended', 'موقوف'), ('on_leave', 'في إجازة')], default='active', max_length=15, verbose_name='حالة التوظيف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='employee_profiles/', verbose_name='صورة الملف الشخصي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='employee_management.department', verbose_name='القسم')),
                ('direct_manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to='employee_management.employee', verbose_name='المدير المباشر')),
                ('user_account', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='حساب المستخدم')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفين',
                'db_table': 'employee_management_employee',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='employee_management.employee', verbose_name='مدير القسم'),
        ),
        migrations.CreateModel(
            name='JobTitle',
            fields=[
                ('job_code', models.AutoField(primary_key=True, serialize=False, verbose_name='رمز الوظيفة')),
                ('job_title', models.CharField(max_length=100, unique=True, verbose_name='المسمى الوظيفي')),
                ('job_title_en', models.CharField(blank=True, max_length=100, verbose_name='المسمى الوظيفي بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('grade_level', models.PositiveIntegerField(default=1, verbose_name='المستوى الوظيفي')),
                ('min_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأدنى للراتب')),
                ('max_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأقصى للراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_titles', to='employee_management.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'وظيفة',
                'verbose_name_plural': 'الوظائف',
                'db_table': 'employee_management_job_title',
                'ordering': ['department', 'grade_level', 'job_title'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='job_title',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='employee_management.jobtitle', verbose_name='المسمى الوظيفي'),
        ),
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الوثيقة')),
                ('document_type', models.CharField(choices=[('contract', 'عقد العمل'), ('id_copy', 'صورة الهوية'), ('passport_copy', 'صورة جواز السفر'), ('certificate', 'شهادة'), ('cv', 'السيرة الذاتية'), ('medical_report', 'تقرير طبي'), ('performance_review', 'تقييم الأداء'), ('disciplinary_action', 'إجراء تأديبي'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الوثيقة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('document_file', models.FileField(upload_to='employee_documents/', verbose_name='ملف الوثيقة')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف')),
                ('document_date', models.DateField(verbose_name='تاريخ الوثيقة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_confidential', models.BooleanField(default=False, verbose_name='سري')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='employee_management.employee', verbose_name='الموظف')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employee_documents_uploaded', to=settings.AUTH_USER_MODEL, verbose_name='رفعت بواسطة')),
            ],
            options={
                'verbose_name': 'وثيقة موظف',
                'verbose_name_plural': 'وثائق الموظفين',
                'db_table': 'employee_management_employee_document',
                'ordering': ['-document_date', '-created_at'],
                'indexes': [models.Index(fields=['employee', 'document_type'], name='employee_ma_employe_148f6a_idx'), models.Index(fields=['document_date'], name='employee_ma_documen_0587ae_idx'), models.Index(fields=['expiry_date'], name='employee_ma_expiry__6f95a2_idx')],
            },
        ),
        migrations.CreateModel(
            name='EmployeeNote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الملاحظة')),
                ('note_type', models.CharField(choices=[('performance', 'تقييم الأداء'), ('disciplinary', 'إجراء تأديبي'), ('achievement', 'إنجاز'), ('training', 'تدريب'), ('general', 'عام'), ('warning', 'تحذير'), ('commendation', 'تقدير')], max_length=15, verbose_name='نوع الملاحظة')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('critical', 'حرج')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('content', models.TextField(verbose_name='محتوى الملاحظة')),
                ('note_date', models.DateField(verbose_name='تاريخ الملاحظة')),
                ('requires_followup', models.BooleanField(default=False, verbose_name='تتطلب متابعة')),
                ('followup_date', models.DateField(blank=True, null=True, verbose_name='تاريخ المتابعة')),
                ('followup_completed', models.BooleanField(default=False, verbose_name='تمت المتابعة')),
                ('is_confidential', models.BooleanField(default=False, verbose_name='سري')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employee_notes_created', to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='employee_management.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ملاحظة موظف',
                'verbose_name_plural': 'ملاحظات الموظفين',
                'db_table': 'employee_management_employee_note',
                'ordering': ['-note_date', '-created_at'],
                'indexes': [models.Index(fields=['employee', 'note_type'], name='employee_ma_employe_082c79_idx'), models.Index(fields=['note_date'], name='employee_ma_note_da_ae6bb6_idx'), models.Index(fields=['priority'], name='employee_ma_priorit_5fe471_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['employee_id'], name='employee_ma_employe_694fe2_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['national_id'], name='employee_ma_nationa_882383_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['email'], name='employee_ma_email_d2c3c4_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['department', 'employment_status'], name='employee_ma_departm_c86014_idx'),
        ),
    ]
