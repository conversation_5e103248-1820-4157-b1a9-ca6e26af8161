{% extends 'base_updated.html' %}
{% load static %}
{% load i18n %}

{% block title %}لوحة تحكم إدارة الموظفين - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific styles */
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        color: white;
        transition: all 0.3s ease;
        height: 100%;
        min-height: 200px;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        height: 100%;
        border-left: 4px solid #667eea;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .stat-icon {
        font-size: 3rem;
        color: #667eea;
        opacity: 0.3;
        position: absolute;
        top: 1rem;
        left: 1rem;
    }

    .quick-action-card {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5253 100%);
        border: none;
        border-radius: 15px;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: block;
        padding: 1.5rem;
        height: 100%;
        min-height: 150px;
    }

    .quick-action-card:hover {
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .quick-action-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .recent-activity-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        height: 100%;
    }

    .activity-item {
        padding: 1rem;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.3s ease;
    }

    .activity-item:hover {
        background-color: #f8f9fa;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
    }

    .activity-icon.new {
        background-color: #28a745;
        color: white;
    }

    .activity-icon.update {
        background-color: #ffc107;
        color: white;
    }

    .activity-icon.delete {
        background-color: #dc3545;
        color: white;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -20px -20px 2rem -20px;
        border-radius: 0 0 20px 20px;
    }

    .breadcrumb {
        background: transparent;
        margin-bottom: 0;
    }

    .breadcrumb-item a {
        color: rgba(255,255,255,0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    /* Chart container */
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        height: 400px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .stat-number {
            font-size: 2rem;
        }
        
        .stat-icon {
            font-size: 2rem;
        }
        
        .quick-action-icon {
            font-size: 2rem;
        }
        
        .page-header {
            margin: -20px -15px 2rem -15px;
        }
    }
</style>
{% endblock %}

{% block page_title %}لوحة تحكم إدارة الموظفين{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'employee_management:dashboard' %}">إدارة الموظفين</a></li>
<li class="breadcrumb-item active">لوحة التحكم</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-users me-3"></i>
                        لوحة تحكم إدارة الموظفين
                    </h1>
                    <p class="mb-0 opacity-75">إدارة شاملة لبيانات الموظفين والأقسام والوظائف</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex justify-content-md-end justify-content-start mt-3 mt-md-0">
                        <a href="{% url 'employee_management:employee_create' %}" class="btn btn-light btn-lg me-2">
                            <i class="fas fa-plus me-2"></i>موظف جديد
                        </a>
                        <a href="{% url 'employee_management:employee_list' %}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list me-2"></i>قائمة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card position-relative">
                <i class="fas fa-users stat-icon"></i>
                <div class="stat-number">{{ total_employees|default:0 }}</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card position-relative">
                <i class="fas fa-building stat-icon"></i>
                <div class="stat-number">{{ total_departments|default:0 }}</div>
                <div class="stat-label">الأقسام</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card position-relative">
                <i class="fas fa-briefcase stat-icon"></i>
                <div class="stat-number">{{ total_job_titles|default:0 }}</div>
                <div class="stat-label">المسميات الوظيفية</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card position-relative">
                <i class="fas fa-user-plus stat-icon"></i>
                <div class="stat-number">{{ new_employees_this_month|default:0 }}</div>
                <div class="stat-label">موظفين جدد هذا الشهر</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="fas fa-bolt me-2"></i>
                إجراءات سريعة
            </h3>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{% url 'employee_management:employee_create' %}" class="quick-action-card">
                <div class="text-center">
                    <i class="fas fa-user-plus quick-action-icon"></i>
                    <h5>إضافة موظف جديد</h5>
                    <p class="mb-0">تسجيل موظف جديد في النظام</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{% url 'employee_management:department_create' %}" class="quick-action-card" style="background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);">
                <div class="text-center">
                    <i class="fas fa-building quick-action-icon"></i>
                    <h5>إضافة قسم جديد</h5>
                    <p class="mb-0">إنشاء قسم جديد في الشركة</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{% url 'employee_management:job_create' %}" class="quick-action-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                <div class="text-center">
                    <i class="fas fa-briefcase quick-action-icon"></i>
                    <h5>إضافة وظيفة جديدة</h5>
                    <p class="mb-0">تعريف مسمى وظيفي جديد</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <a href="{% url 'employee_management:employee_list' %}" class="quick-action-card" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">
                <div class="text-center">
                    <i class="fas fa-search quick-action-icon"></i>
                    <h5>البحث في الموظفين</h5>
                    <p class="mb-0">البحث والتصفية المتقدم</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-6 mb-4">
            <div class="recent-activity-card">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        النشاطات الأخيرة
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon {{ activity.type }}">
                                <i class="fas fa-{{ activity.icon }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">{{ activity.title }}</div>
                                <small class="text-muted">{{ activity.description }}</small>
                                <div class="text-muted small">{{ activity.created_at|timesince }} مضت</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد نشاطات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Department Overview -->
        <div class="col-lg-6 mb-4">
            <div class="recent-activity-card">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        نظرة عامة على الأقسام
                    </h5>
                </div>
                <div class="card-body">
                    {% if departments %}
                        {% for dept in departments %}
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" style="background-color: #f8f9fa;">
                            <div>
                                <div class="fw-bold">{{ dept.dept_name }}</div>
                                <small class="text-muted">{{ dept.employee_count }} موظف</small>
                            </div>
                            <div class="text-end">
                                <a href="{% url 'employee_management:department_detail' dept.pk %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{% url 'employee_management:department_list' %}" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>عرض جميع الأقسام
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد أقسام مسجلة</p>
                            <a href="{% url 'employee_management:department_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة قسم جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation to Other HR Modules -->
    <div class="row">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="fas fa-sitemap me-2"></i>
                وحدات الموارد البشرية الأخرى
            </h3>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <a href="{% url 'leave_management:dashboard' %}" class="dashboard-card card text-decoration-none" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                    <h5>إدارة الإجازات</h5>
                    <p class="mb-0">إدارة طلبات الإجازات وأرصدة الموظفين</p>
                </div>
            </a>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <a href="{% url 'attendance_system:dashboard' %}" class="dashboard-card card text-decoration-none" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <h5>نظام الحضور والانصراف</h5>
                    <p class="mb-0">تتبع حضور الموظفين وساعات العمل</p>
                </div>
            </a>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <a href="{% url 'payroll_management:dashboard' %}" class="dashboard-card card text-decoration-none" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                    <h5>إدارة الرواتب</h5>
                    <p class="mb-0">حساب ومعالجة رواتب الموظفين</p>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any dashboard-specific JavaScript here
    console.log('Employee Management Dashboard loaded');
    
    // Auto-refresh statistics every 5 minutes
    setInterval(function() {
        // You can add AJAX calls here to refresh statistics
        console.log('Refreshing dashboard statistics...');
    }, 300000); // 5 minutes
});
</script>
{% endblock %}
