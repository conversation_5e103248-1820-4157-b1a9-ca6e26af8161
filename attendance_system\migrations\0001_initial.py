# Generated by Django 5.0.14 on 2025-06-22 14:27

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employee_management', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceDevice',
            fields=[
                ('device_id', models.AutoField(primary_key=True, serialize=False, verbose_name='معرف الجهاز')),
                ('device_name', models.CharField(max_length=100, unique=True, verbose_name='اسم الجهاز')),
                ('device_type', models.CharField(choices=[('zk_teco', 'ZKTeco'), ('hikvision', 'Hikvision'), ('dahua', 'Dahua'), ('suprema', 'Suprema'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الجهاز')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('port', models.PositiveIntegerField(default=4370, verbose_name='المنفذ')),
                ('connection_type', models.CharField(choices=[('tcp', 'TCP/IP'), ('udp', 'UDP'), ('serial', 'Serial'), ('usb', 'USB')], default='tcp', max_length=10, verbose_name='نوع الاتصال')),
                ('serial_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم التسلسلي')),
                ('firmware_version', models.CharField(blank=True, max_length=20, verbose_name='إصدار البرنامج الثابت')),
                ('max_users', models.PositiveIntegerField(default=1000, verbose_name='الحد الأقصى للمستخدمين')),
                ('max_records', models.PositiveIntegerField(default=100000, verbose_name='الحد الأقصى للسجلات')),
                ('location', models.CharField(max_length=100, verbose_name='الموقع')),
                ('auto_sync_enabled', models.BooleanField(default=True, verbose_name='المزامنة التلقائية مفعلة')),
                ('sync_interval_minutes', models.PositiveIntegerField(default=15, verbose_name='فترة المزامنة (دقائق)')),
                ('last_sync_time', models.DateTimeField(blank=True, null=True, verbose_name='آخر وقت مزامنة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_online', models.BooleanField(default=False, verbose_name='متصل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendance_devices', to='employee_management.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'جهاز الحضور',
                'verbose_name_plural': 'أجهزة الحضور',
                'db_table': 'attendance_system_device',
                'ordering': ['device_name'],
            },
        ),
        migrations.CreateModel(
            name='AttendanceRule',
            fields=[
                ('rule_id', models.AutoField(primary_key=True, serialize=False, verbose_name='معرف القاعدة')),
                ('rule_name', models.CharField(max_length=100, verbose_name='اسم القاعدة')),
                ('rule_type', models.CharField(choices=[('work_schedule', 'جدول العمل'), ('overtime', 'العمل الإضافي'), ('break_time', 'وقت الاستراحة'), ('grace_period', 'فترة السماح'), ('late_penalty', 'جزاء التأخير')], max_length=20, verbose_name='نوع القاعدة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('work_start_time', models.TimeField(blank=True, null=True, verbose_name='وقت بداية العمل')),
                ('work_end_time', models.TimeField(blank=True, null=True, verbose_name='وقت نهاية العمل')),
                ('break_start_time', models.TimeField(blank=True, null=True, verbose_name='وقت بداية الاستراحة')),
                ('break_end_time', models.TimeField(blank=True, null=True, verbose_name='وقت نهاية الاستراحة')),
                ('late_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة السماح للتأخير (دقائق)')),
                ('early_departure_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة السماح للانصراف المبكر (دقائق)')),
                ('working_days', models.JSONField(default=list, verbose_name='أيام العمل')),
                ('overtime_threshold_minutes', models.PositiveIntegerField(default=480, verbose_name='حد العمل الإضافي (دقائق)')),
                ('overtime_multiplier', models.DecimalField(decimal_places=2, default=1.5, max_digits=3, verbose_name='مضاعف العمل الإضافي')),
                ('applies_to_all', models.BooleanField(default=True, verbose_name='تطبق على الجميع')),
                ('effective_from', models.DateField(verbose_name='ساري من')),
                ('effective_to', models.DateField(blank=True, null=True, verbose_name='ساري حتى')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('departments', models.ManyToManyField(blank=True, related_name='attendance_rules', to='employee_management.department', verbose_name='الأقسام المحددة')),
                ('job_titles', models.ManyToManyField(blank=True, related_name='attendance_rules', to='employee_management.jobtitle', verbose_name='الوظائف المحددة')),
            ],
            options={
                'verbose_name': 'قاعدة الحضور',
                'verbose_name_plural': 'قواعد الحضور',
                'db_table': 'attendance_system_rule',
                'ordering': ['rule_name'],
            },
        ),
        migrations.CreateModel(
            name='AttendanceException',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('attendance_date', models.DateField(verbose_name='تاريخ الحضور')),
                ('exception_type', models.CharField(choices=[('manual_adjustment', 'تعديل يدوي'), ('missing_punch', 'تسجيل مفقود'), ('system_error', 'خطأ في النظام'), ('device_malfunction', 'عطل في الجهاز'), ('approved_overtime', 'عمل إضافي معتمد'), ('field_work', 'عمل ميداني'), ('training', 'تدريب'), ('meeting', 'اجتماع'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الاستثناء')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('adjusted_check_in', models.TimeField(blank=True, null=True, verbose_name='وقت الدخول المعدل')),
                ('adjusted_check_out', models.TimeField(blank=True, null=True, verbose_name='وقت الخروج المعدل')),
                ('adjusted_work_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='ساعات العمل المعدلة')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('supporting_document', models.FileField(blank=True, null=True, upload_to='attendance_exceptions/', verbose_name='المستند المؤيد')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('is_applied', models.BooleanField(default=False, verbose_name='مطبق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_attendance_exceptions', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_exceptions', to='employee_management.employee', verbose_name='الموظف')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='requested_attendance_exceptions', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
            ],
            options={
                'verbose_name': 'استثناء الحضور',
                'verbose_name_plural': 'استثناءات الحضور',
                'db_table': 'attendance_system_exception',
                'ordering': ['-attendance_date', '-created_at'],
                'indexes': [models.Index(fields=['employee', 'attendance_date'], name='attendance__employe_c36cc9_idx'), models.Index(fields=['exception_type', 'is_approved'], name='attendance__excepti_adbc17_idx'), models.Index(fields=['requested_by', 'created_at'], name='attendance__request_202c52_idx')],
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('record_id', models.AutoField(primary_key=True, serialize=False, verbose_name='معرف السجل')),
                ('punch_time', models.DateTimeField(verbose_name='وقت التسجيل')),
                ('punch_type', models.CharField(choices=[('check_in', 'دخول'), ('check_out', 'خروج'), ('break_out', 'خروج استراحة'), ('break_in', 'عودة من الاستراحة'), ('overtime_in', 'دخول عمل إضافي'), ('overtime_out', 'خروج عمل إضافي')], max_length=15, verbose_name='نوع التسجيل')),
                ('verification_method', models.CharField(choices=[('fingerprint', 'بصمة الإصبع'), ('face', 'التعرف على الوجه'), ('card', 'البطاقة'), ('password', 'كلمة المرور'), ('manual', 'يدوي')], max_length=15, verbose_name='طريقة التحقق')),
                ('device_user_id', models.CharField(max_length=20, verbose_name='معرف المستخدم في الجهاز')),
                ('device_record_id', models.CharField(blank=True, max_length=50, verbose_name='معرف السجل في الجهاز')),
                ('is_processed', models.BooleanField(default=False, verbose_name='تم المعالجة')),
                ('is_valid', models.BooleanField(default=True, verbose_name='صحيح')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('sync_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت المزامنة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='attendance_system.attendancedevice', verbose_name='الجهاز')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='employee_management.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'سجل الحضور',
                'verbose_name_plural': 'سجلات الحضور',
                'db_table': 'attendance_system_record',
                'ordering': ['-punch_time'],
                'indexes': [models.Index(fields=['employee', 'punch_time'], name='attendance__employe_50f62f_idx'), models.Index(fields=['device', 'punch_time'], name='attendance__device__491dfd_idx'), models.Index(fields=['punch_time', 'punch_type'], name='attendance__punch_t_cf08ba_idx'), models.Index(fields=['is_processed', 'is_valid'], name='attendance__is_proc_720856_idx')],
                'unique_together': {('device', 'device_record_id')},
            },
        ),
        migrations.CreateModel(
            name='DailyAttendance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('attendance_date', models.DateField(verbose_name='تاريخ الحضور')),
                ('check_in_time', models.TimeField(blank=True, null=True, verbose_name='وقت الدخول')),
                ('check_out_time', models.TimeField(blank=True, null=True, verbose_name='وقت الخروج')),
                ('break_out_time', models.TimeField(blank=True, null=True, verbose_name='وقت خروج الاستراحة')),
                ('break_in_time', models.TimeField(blank=True, null=True, verbose_name='وقت عودة الاستراحة')),
                ('total_work_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4, verbose_name='إجمالي ساعات العمل')),
                ('break_duration_minutes', models.PositiveIntegerField(default=0, verbose_name='مدة الاستراحة (دقائق)')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4, verbose_name='ساعات العمل الإضافي')),
                ('late_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق التأخير')),
                ('early_departure_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق الانصراف المبكر')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('early_departure', 'انصراف مبكر'), ('incomplete', 'غير مكتمل'), ('holiday', 'عطلة'), ('leave', 'إجازة'), ('weekend', 'عطلة نهاية الأسبوع')], max_length=20, verbose_name='الحالة')),
                ('is_holiday', models.BooleanField(default=False, verbose_name='عطلة')),
                ('is_weekend', models.BooleanField(default=False, verbose_name='عطلة نهاية الأسبوع')),
                ('is_on_leave', models.BooleanField(default=False, verbose_name='في إجازة')),
                ('comments', models.TextField(blank=True, verbose_name='التعليقات')),
                ('is_processed', models.BooleanField(default=False, verbose_name='تم المعالجة')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت المعالجة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('attendance_rule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='daily_attendance', to='attendance_system.attendancerule', verbose_name='قاعدة الحضور المطبقة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_attendance', to='employee_management.employee', verbose_name='الموظف')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_attendance', to=settings.AUTH_USER_MODEL, verbose_name='معالج بواسطة')),
            ],
            options={
                'verbose_name': 'الحضور اليومي',
                'verbose_name_plural': 'الحضور اليومي',
                'db_table': 'attendance_system_daily_attendance',
                'ordering': ['-attendance_date', 'employee'],
                'indexes': [models.Index(fields=['employee', 'attendance_date'], name='attendance__employe_11a3a1_idx'), models.Index(fields=['attendance_date', 'status'], name='attendance__attenda_7e9aaa_idx'), models.Index(fields=['is_processed', 'attendance_date'], name='attendance__is_proc_1f11e9_idx')],
                'unique_together': {('employee', 'attendance_date')},
            },
        ),
        migrations.CreateModel(
            name='MonthlyAttendanceSummary',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('year', models.PositiveIntegerField(verbose_name='السنة')),
                ('month', models.PositiveIntegerField(verbose_name='الشهر')),
                ('total_working_days', models.PositiveIntegerField(default=0, verbose_name='إجمالي أيام العمل')),
                ('present_days', models.PositiveIntegerField(default=0, verbose_name='أيام الحضور')),
                ('absent_days', models.PositiveIntegerField(default=0, verbose_name='أيام الغياب')),
                ('late_days', models.PositiveIntegerField(default=0, verbose_name='أيام التأخير')),
                ('early_departure_days', models.PositiveIntegerField(default=0, verbose_name='أيام الانصراف المبكر')),
                ('total_work_hours', models.DecimalField(decimal_places=2, default=0, max_digits=6, verbose_name='إجمالي ساعات العمل')),
                ('total_overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=6, verbose_name='إجمالي ساعات العمل الإضافي')),
                ('total_late_minutes', models.PositiveIntegerField(default=0, verbose_name='إجمالي دقائق التأخير')),
                ('total_early_departure_minutes', models.PositiveIntegerField(default=0, verbose_name='إجمالي دقائق الانصراف المبكر')),
                ('leave_days', models.PositiveIntegerField(default=0, verbose_name='أيام الإجازة')),
                ('holiday_days', models.PositiveIntegerField(default=0, verbose_name='أيام العطل')),
                ('weekend_days', models.PositiveIntegerField(default=0, verbose_name='أيام عطلة نهاية الأسبوع')),
                ('attendance_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الحضور')),
                ('punctuality_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الالتزام بالمواعيد')),
                ('is_finalized', models.BooleanField(default=False, verbose_name='مؤكد')),
                ('finalized_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التأكيد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='monthly_attendance_summaries', to='employee_management.employee', verbose_name='الموظف')),
                ('finalized_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='finalized_monthly_summaries', to=settings.AUTH_USER_MODEL, verbose_name='أكد بواسطة')),
            ],
            options={
                'verbose_name': 'ملخص الحضور الشهري',
                'verbose_name_plural': 'ملخصات الحضور الشهرية',
                'db_table': 'attendance_system_monthly_summary',
                'ordering': ['-year', '-month', 'employee'],
                'indexes': [models.Index(fields=['employee', 'year', 'month'], name='attendance__employe_890f49_idx'), models.Index(fields=['year', 'month'], name='attendance__year_8e8ab6_idx'), models.Index(fields=['is_finalized'], name='attendance__is_fina_de0e02_idx')],
                'unique_together': {('employee', 'year', 'month')},
            },
        ),
    ]
